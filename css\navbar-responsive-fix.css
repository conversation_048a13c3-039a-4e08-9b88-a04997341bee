/* Navbar Responsiveness Fix for 1024px Breakpoint */

/* Fix for navbar at 1024px laptop breakpoint */
@media (max-width: 1024px) and (min-width: 992px) {
    /* Ensure header container has proper spacing */
    #site-header__container {
        padding: 0 15px;
    }
    
    /* Adjust header wrapper to prevent text overlapping */
    #site-header__wrapper {
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
    }
    
    /* Ensure logo doesn't get too large */
    #site-header__left .logo img {
        max-width: 150px;
        height: auto;
    }
    
    /* Adjust menu spacing and font size for better fit */
    .header-menu .menu {
        gap: 15px !important;
        font-size: 14px;
    }
    
    /* Reduce padding on menu items */
    .header-menu .menu li a {
        padding: 6px 8px !important;
        font-size: 14px;
    }
    
    /* Ensure register button doesn't get too wide */
    .header-menu .menu li a[href*="register"] {
        padding: 6px 12px !important;
        white-space: nowrap;
    }
    
    /* Prevent text from overlapping images */
    #site-header {
        z-index: 1000;
        position: relative;
    }
    
    /* Ensure proper spacing for content below header */
    #site-content {
        margin-top: 0;
        padding-top: 0;
    }
    
    /* Fix any potential overflow issues */
    #site-header__right {
        overflow: visible;
        flex-shrink: 0;
    }
    
    /* Ensure mobile triggers are properly hidden at this breakpoint */
    .mobile-triggers {
        display: none !important;
    }
    
    /* Make sure desktop menu is visible */
    .header-menu .menu.d-none.d-lg-flex {
        display: flex !important;
    }
}

/* Additional fix for exactly 1024px width */
@media (width: 1024px) {
    /* More aggressive spacing reduction for exact 1024px */
    .header-menu .menu {
        gap: 12px !important;
    }
    
    .header-menu .menu li a {
        padding: 5px 6px !important;
        font-size: 13px;
    }
    
    #site-header__left .logo img {
        max-width: 140px;
    }
}

/* Fix for content sections that might overlap with navbar */
@media (max-width: 1024px) and (min-width: 992px) {
    /* Ensure intro section doesn't overlap */
    .intro-section,
    .hero-section,
    .main-content {
        margin-top: 20px;
    }
    
    /* Fix any absolute positioned elements that might overlap */
    .intro-scroll__trigger {
        z-index: 999;
    }
    
    /* Ensure proper spacing for any background images */
    .intro-bg,
    .hero-bg {
        z-index: -1;
    }
}

/* Fallback for very specific 1024px issues */
@media (max-width: 1025px) and (min-width: 1023px) {
    /* Force proper layout at this specific breakpoint */
    #site-header__wrapper {
        min-height: 60px;
        padding: 10px 0;
    }
    
    /* Ensure no text wrapping in menu */
    .header-menu .menu li {
        white-space: nowrap;
    }
    
    /* Prevent any flex shrinking that might cause overlap */
    #site-header__left,
    #site-header__right {
        flex-shrink: 0;
    }
}
