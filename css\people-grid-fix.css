/* Fix for displaying all 5 people cards in one line */

.related-content__grid.g-flex {
    display: flex !important;
    flex-wrap: nowrap !important;
    gap: 15px !important;
    overflow-x: auto !important;
    padding: 10px 0 !important;
}

.related-content__grid.g-flex .post-box.people {
    flex: 0 0 auto !important;
    min-width: 200px !important;
    max-width: 250px !important;
    width: calc(20% - 12px) !important;
}

/* Ensure images maintain aspect ratio */
.related-content__grid.g-flex .post-box.people .image-container {
    width: 100% !important;
    height: auto !important;
}

.related-content__grid.g-flex .post-box.people .image-wrapper {
    width: 100% !important;
    height: 140px !important;
    overflow: hidden !important;
}

.related-content__grid.g-flex .post-box.people .image-wrapper img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}

/* Adjust text container */
.related-content__grid.g-flex .post-box.people .text-container {
    padding: 10px 5px !important;
}

.related-content__grid.g-flex .post-box.people .post-title {
    font-size: 14px !important;
    line-height: 1.2 !important;
    margin-bottom: 5px !important;
}

.related-content__grid.g-flex .post-box.people .designation {
    font-size: 12px !important;
    line-height: 1.3 !important;
    margin-bottom: 3px !important;
}

.related-content__grid.g-flex .post-box.people .department {
    font-size: 11px !important;
    margin-bottom: 3px !important;
}

.related-content__grid.g-flex .post-box.people .location {
    font-size: 11px !important;
    margin-bottom: 0 !important;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .related-content__grid.g-flex .post-box.people {
        min-width: 180px !important;
        max-width: 220px !important;
    }
}

@media (max-width: 992px) {
    .related-content__grid.g-flex .post-box.people {
        min-width: 160px !important;
        max-width: 200px !important;
    }
    
    .related-content__grid.g-flex .post-box.people .image-wrapper {
        height: 120px !important;
    }
}

@media (max-width: 768px) {
    .related-content__grid.g-flex {
        flex-wrap: wrap !important;
        justify-content: center !important;
    }
    
    .related-content__grid.g-flex .post-box.people {
        width: calc(50% - 10px) !important;
        min-width: 150px !important;
        max-width: 180px !important;
    }
}

@media (max-width: 480px) {
    .related-content__grid.g-flex .post-box.people {
        width: 100% !important;
        max-width: 300px !important;
    }
}
